import type { IBackendRes } from "@/types/backend";
import axiosClient from "axios";
import { store } from "@/redux/store";
import { setRefreshTokenAction } from "@/redux/slice/accountSlice";

interface AccessTokenResponse {
  access_token: string;
}

/**
 * Creates an initial 'axios' instance with custom settings.
 */
const instance = axiosClient.create({
  baseURL: import.meta.env.VITE_BACKEND_URL as string,
  withCredentials: false, // Changed to false since our backend doesn't use cookies
});

const NO_RETRY_HEADER = 'x-no-retry';

// Refresh token functionality removed - users need to login again when token expires

instance.interceptors.request.use(function (config) {
  if (typeof window !== "undefined" && window && window.localStorage && window.localStorage.getItem('access_token')) {
    config.headers.Authorization = 'Bearer ' + window.localStorage.getItem('access_token');
  }
  if (!config.headers.Accept && config.headers["Content-Type"]) {
    config.headers.Accept = "application/json";
    config.headers["Content-Type"] = "application/json; charset=utf-8";
  }
  return config;
});

/**
 * Handle all responses. It is possible to add handlers
 * for requests, but it is omitted here for brevity.
 */
instance.interceptors.response.use(
  (res) => res,
  async (error) => {
    // When token expires (401), redirect to login
    if (error.config && error.response
      && +error.response.status === 401
      && error.config.url !== '/auth/login'
    ) {
      const message = error?.response?.data?.message ?? "Session expired, please login again.";
      //dispatch redux action
      store.dispatch(setRefreshTokenAction({ status: true, message }));
    }

    return error?.response?.data ?? Promise.reject(error);
  }
);

export default instance;
