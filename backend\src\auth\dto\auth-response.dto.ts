import { ApiProperty } from '@nestjs/swagger';

export class UserResponseDto {
  @ApiProperty({ example: 1, description: 'User ID' })
  id: number;

  @ApiProperty({ example: '<EMAIL>', description: 'User email' })
  email: string;

  @ApiProperty({ example: '<PERSON>', description: 'User name', required: false })
  name?: string | null;

  @ApiProperty({ example: 'https://example.com/avatar.png', description: 'User avatar', required: false })
  avatar?: string | null;

  @ApiProperty({ example: 'user', description: 'User role' })
  roles: string;
}

export class AuthResponseDto {
  @ApiProperty({ example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...', description: 'JWT access token' })
  access_token: string;

  @ApiProperty({ type: UserResponseDto, description: 'User information' })
  user: UserResponseDto;
}



export class ForgotPasswordResponseDto {
  @ApiProperty({ example: '<NAME_EMAIL> exists, a reset link will be sent.', description: 'Response message' })
  message: string;
}

export class LogoutResponseDto {
  @ApiProperty({ example: 'Logout successful. Please remove the token from client storage.', description: 'Logout message' })
  message: string;
}
