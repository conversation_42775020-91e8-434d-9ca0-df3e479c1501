import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
  BadRequestException,
  UnauthorizedException,
  ForbiddenException,
  NotFoundException,
  ConflictException,
  InternalServerErrorException
} from '@nestjs/common';
import { Request, Response } from 'express';
import { Prisma } from '@prisma/client';

export interface StandardErrorResponse {
  ok: boolean;
  errors: string[];
  data: null;
}

@Catch()
export class HttpExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(HttpExceptionFilter.name);

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let errors: string[] = [];

    // Handle different types of exceptions
    if (exception instanceof HttpException) {
      status = exception.getStatus();
      errors = this.extractHttpExceptionErrors(exception);
    } else if (exception instanceof Prisma.PrismaClientKnownRequestError) {
      const result = this.handlePrismaError(exception);
      status = result.status;
      errors = result.errors;
    } else if (exception instanceof Prisma.PrismaClientValidationError) {
      status = HttpStatus.BAD_REQUEST;
      errors = ['Invalid data provided'];
    } else if (exception instanceof Error) {
      // Log unexpected errors
      this.logger.error(`Unexpected error: ${exception.message}`, exception.stack);
      errors = ['Internal server error'];
    } else {
      // Log unknown exceptions
      this.logger.error('Unknown exception occurred', exception);
      errors = ['Internal server error'];
    }

    // Create standardized error response
    const errorResponse: StandardErrorResponse = {
      ok: false,
      errors,
      data: null,
    };

    // Log error for debugging (except validation errors)
    if (status >= 500) {
      this.logger.error(
        `${request.method} ${request.url} - ${status} - ${errors.join(', ')}`,
        exception instanceof Error ? exception.stack : exception
      );
    }

    response.status(status).json(errorResponse);
  }

  private extractHttpExceptionErrors(exception: HttpException): string[] {
    const exceptionResponse = exception.getResponse();

    if (typeof exceptionResponse === 'string') {
      return [exceptionResponse];
    }

    if (typeof exceptionResponse === 'object' && exceptionResponse !== null) {
      const responseObj = exceptionResponse as any;

      if (responseObj.message) {
        if (Array.isArray(responseObj.message)) {
          return responseObj.message;
        } else {
          return [responseObj.message];
        }
      } else if (responseObj.error) {
        return [responseObj.error];
      }
    }

    return ['An error occurred'];
  }

  private handlePrismaError(exception: Prisma.PrismaClientKnownRequestError): { status: number; errors: string[] } {
    switch (exception.code) {
      case 'P2002':
        // Unique constraint violation
        const target = exception.meta?.target as string[] | undefined;
        const field = target?.[0] || 'field';
        return {
          status: HttpStatus.CONFLICT,
          errors: [`${field.charAt(0).toUpperCase() + field.slice(1)} already exists`]
        };

      case 'P2025':
        // Record not found
        return {
          status: HttpStatus.NOT_FOUND,
          errors: ['Record not found']
        };

      case 'P2003':
        // Foreign key constraint violation
        return {
          status: HttpStatus.BAD_REQUEST,
          errors: ['Invalid reference to related record']
        };

      case 'P2014':
        // Required relation violation
        return {
          status: HttpStatus.BAD_REQUEST,
          errors: ['Required relation is missing']
        };

      default:
        this.logger.error(`Unhandled Prisma error: ${exception.code}`, exception);
        return {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          errors: ['Database error occurred']
        };
    }
  }
}