import { ExceptionFilter, Catch, ArgumentsHost, HttpException, HttpStatus } from '@nestjs/common';
import { Request, Response } from 'express';

export interface StandardErrorResponse {
  ok: boolean;
  errors: string[];
  data: null;
}

@Catch()
export class HttpExceptionFilter implements ExceptionFilter {
  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    const status = exception instanceof HttpException
      ? exception.getStatus()
      : HttpStatus.INTERNAL_SERVER_ERROR;

    // Extract error messages
    let errors: string[] = [];

    if (exception instanceof HttpException) {
      const exceptionResponse = exception.getResponse();

      if (typeof exceptionResponse === 'string') {
        errors = [exceptionResponse];
      } else if (typeof exceptionResponse === 'object' && exceptionResponse !== null) {
        const responseObj = exceptionResponse as any;

        if (responseObj.message) {
          if (Array.isArray(responseObj.message)) {
            errors = responseObj.message;
          } else {
            errors = [responseObj.message];
          }
        } else if (responseObj.error) {
          errors = [responseObj.error];
        } else {
          errors = ['An error occurred'];
        }
      }
    } else {
      errors = ['Internal server error'];
    }

    // Create standardized error response
    const errorResponse: StandardErrorResponse = {
      ok: false,
      errors,
      data: null,
    };

    response.status(status).json(errorResponse);
  }
}