import { ApiProperty } from '@nestjs/swagger';
import { Is<PERSON>mail, IsNotEmpty, MaxLength } from 'class-validator';
export class ForgotPasswordDto {
  @ApiProperty({ example: '<EMAIL>', description: 'User email' })
  @IsEmail({}, { message: 'Please provide a valid email address' })
  @IsNotEmpty({ message: 'Email is required' })
  @MaxLength(255, { message: 'Email cannot exceed 255 characters' })
  email: string;
}