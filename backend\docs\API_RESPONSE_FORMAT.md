# API Response Format

Tất cả API endpoints trong hệ thống đều sử dụng format response chuẩn để đảm bảo t<PERSON>h nhất quán.

## Success Response Format

```json
{
  "ok": true,
  "errors": null,
  "data": {
    // Actual response data here
  }
}
```

## Error Response Format

```json
{
  "ok": false,
  "errors": [
    "Error message 1",
    "Error message 2"
  ],
  "data": null
}
```

## Examples

### 1. Successful Login
```json
{
  "ok": true,
  "errors": null,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": 1,
      "email": "<EMAIL>",
      "name": "<PERSON>",
      "roles": "user"
    }
  }
}
```

### 2. Login Error (Invalid Credentials)
```json
{
  "ok": false,
  "errors": [
    "Invalid credentials"
  ],
  "data": null
}
```

### 3. Validation Error (Multiple Fields)
```json
{
  "ok": false,
  "errors": [
    "Email must be a valid email address",
    "Password must be at least 6 characters long"
  ],
  "data": null
}
```

### 4. Database Error (Unique Constraint)
```json
{
  "ok": false,
  "errors": [
    "Email already exists"
  ],
  "data": null
}
```

### 5. Not Found Error
```json
{
  "ok": false,
  "errors": [
    "Task with ID 999 not found"
  ],
  "data": null
}
```

### 6. Permission Error
```json
{
  "ok": false,
  "errors": [
    "You do not have permission to access this resource"
  ],
  "data": null
}
```

## HTTP Status Codes

- **200**: Success
- **400**: Bad Request (validation errors, invalid data)
- **401**: Unauthorized (authentication required)
- **403**: Forbidden (insufficient permissions)
- **404**: Not Found
- **409**: Conflict (duplicate data)
- **500**: Internal Server Error

## Error Types Handled

1. **Validation Errors**: From DTO validation using class-validator
2. **Authentication Errors**: Invalid credentials, expired tokens
3. **Authorization Errors**: Insufficient permissions
4. **Database Errors**: Prisma errors (unique constraints, foreign keys, etc.)
5. **Business Logic Errors**: Custom application errors
6. **System Errors**: Unexpected server errors

## Implementation Details

- **Exception Filter**: `HttpExceptionFilter` handles all exceptions
- **Response Interceptor**: `ResponseInterceptor` formats success responses
- **Validation Pipe**: Custom validation pipe formats validation errors
- **Prisma Error Handling**: Automatic conversion of Prisma errors to user-friendly messages
