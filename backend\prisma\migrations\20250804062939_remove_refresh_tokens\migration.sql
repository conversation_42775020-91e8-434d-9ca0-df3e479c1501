/*
  Warnings:

  - You are about to drop the `refresh_tokens` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE `refresh_tokens` DROP FOREIGN KEY `refresh_tokens_userId_fkey`;

-- DropTable
DROP TABLE `refresh_tokens`;

-- CreateIndex
CREATE INDEX `idx_projects_owner_status` ON `projects`(`ownerId`, `status`);

-- CreateIndex
CREATE INDEX `idx_tasks_user_project` ON `tasks`(`userId`, `projectId`);

-- CreateIndex
CREATE INDEX `idx_users_email_covering` ON `users`(`email`, `id`, `name`, `roles`);
