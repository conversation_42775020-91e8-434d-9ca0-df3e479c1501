import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { StandardSuccessResponse } from '../interfaces/api-response.interface';

@Injectable()
export class ResponseInterceptor<T> implements NestInterceptor<T, StandardSuccessResponse<T>> {
  intercept(context: ExecutionContext, next: CallHandler): Observable<StandardSuccessResponse<T>> {
    return next.handle().pipe(
      map(data => ({
        ok: true,
        errors: null,
        data,
      })),
    );
  }
}
